<template>
  <div class="account-details">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="account-details-content">
      <div class="account-title">Account Details</div>

      <div class="account-info">
        <div class="user-email">{{ userEmail }}</div>
        <div class="account-actions">
          <van-button type="danger" class="action-button" @click="handleLogOut"> Logout </van-button>

          <van-button type="danger" class="action-button" @click="handlePanelList"> Panel List </van-button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <van-dialog
      v-model="showLogoutDialog"
      title="Confirm Logout"
      message="Are you sure you want to log out?"
      show-cancel-button
      @confirm="confirmLogout"
      @cancel="showLogoutDialog = false"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'

export default {
  name: 'AccountDetails',
  components: {
    NavBar
  },
  data() {
    return {
      showLogoutDialog: false
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('alarmSystem', ['alarmSystemType', 'userInfo']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    userEmail() {
      return this.userInfo?.email || '<EMAIL>'
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleLogOut() {
      this.showLogoutDialog = true
    },
    handlePanelList() {
      // 跳转到面板列表页面
      this.$router.push('/alarmSystem/panelList')
    },
    confirmLogout() {
      this.showLogoutDialog = false

      // 显示加载提示
      this.$toast.loading({
        message: 'Logging out...',
        forbidClick: true,
        duration: 1000
      })

      // 模拟登出操作
      setTimeout(() => {
        this.$toast.clear()

        // 清除用户登录状态
        this.$store.commit('alarmSystem/CLEAR_USER_INFO')

        // 跳转到登录页面
        this.$router.replace('/alarmSystem/chooseSystem')

        this.$toast.success('Logged out successfully')
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.account-details {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .account-title {
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
    text-align: left;
  }

  .account-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 10px;
  }

  .user-email {
    font-size: 16px;
    color: #fff;
    font-weight: 400;
    margin-top: 8px;
  }

  .account-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .action-button {
      height: 40px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      border: none;
      min-width: 100px;
      padding: 0 16px;
      background-color: #ff4444;
      color: #fff;

      &:active {
        background-color: #e63939;
      }
    }
  }
}

// 自定义对话框样式
::v-deep .van-dialog {
  .van-dialog__header {
    color: #333;
    font-weight: 600;
  }

  .van-dialog__message {
    color: #666;
    text-align: center;
  }

  .van-dialog__footer {
    .van-button {
      border: none;

      &.van-button--default {
        color: #666;
      }

      &.van-button--primary {
        background-color: #1989fa;
      }
    }
  }
}
</style>
