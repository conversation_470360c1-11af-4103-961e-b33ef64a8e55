<template>
  <div class="panel-list">
    <nav-bar title="Alarm System" @clickLeft="goBack" :showPlus="true" @showPlus="addPanel" />
    <div class="panel-list-content">
      <div class="panel-items" v-if="panelList.length > 0">
        <div v-for="(panel, index) in panelList" :key="index" class="panel-item">
          <div class="panel-header">
            <div class="panel-info">
              <div class="panel-name">{{ panel.name }}</div>
              <div class="panel-id">{{ panel.serialNumber }}</div>
            </div>
            <div class="panel-login" @click="loginPanel(panel)">
              <van-icon name="sign" />
            </div>
          </div>

          <div class="panel-actions">
            <div class="action-item" @click="editPanel(panel)">
              <van-icon name="edit" />
            </div>
            <div class="action-item" @click="deletePanel(panel)">
              <van-icon name="delete-o" />
            </div>
          </div>
        </div>
      </div>

      <div class="empty-state" v-else>
        <div class="empty-icon">
          <van-icon name="warning-o" />
        </div>
        <div class="empty-text">No panels found</div>
        <div class="empty-description">Please check your connection or contact support</div>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import { mapState } from 'vuex'

export default {
  name: 'PanelList',
  components: {
    NavBar
  },
  data() {
    return {
      panelList: [
        {
          id: 1,
          name: 'dsc',
          serialNumber: '210602188712',
          status: 'online'
        },
        {
          id: 2,
          name: 'DSC IL 1234',
          serialNumber: '210602189212',
          status: 'online'
        },
        {
          id: 3,
          name: 'sz visonic',
          serialNumber: '29C267',
          status: 'online'
        },
        {
          id: 4,
          name: 'Visonic IL 4444',
          serialNumber: '2A8448',
          status: 'online'
        }
      ]
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('alarmSystem', ['alarmSystemType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  created() {
    this.loadPanels()
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    loadPanels() {
      // 这里可以调用API加载面板列表
      // 目前使用模拟数据
      this.$toast.loading({
        message: 'Loading panels...',
        forbidClick: true,
        duration: 1000
      })

      setTimeout(() => {
        this.$toast.clear()
      }, 1000)
    },
    loginPanel(panel) {
      this.$toast(`Logging into ${panel.name}...`)
      // 这里可以实现登录面板的逻辑
    },
    editPanel(panel) {
      this.$toast(`Edit ${panel.name}`)
      // 这里可以跳转到编辑面板页面
    },
    deletePanel(panel) {
      this.$dialog
        .confirm({
          title: 'Delete Panel',
          message: `Are you sure you want to delete ${panel.name}?`
        })
        .then(() => {
          // 删除面板逻辑
          const index = this.panelList.findIndex(p => p.id === panel.id)
          if (index > -1) {
            this.panelList.splice(index, 1)
            this.$toast.success('Panel deleted successfully')
          }
        })
        .catch(() => {
          // 取消删除
        })
    },
    addPanel() {
      // 跳转到新增面板页面
      this.$router.push('/alarmSystem/addPanel')
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-list {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .panel-items {
    flex: 1;
    margin-bottom: 20px;
  }

  .panel-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px 16px 0px 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .panel-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 16px;

      .panel-info {
        flex: 1;

        .panel-name {
          font-size: 16px;
          color: #fff;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .panel-id {
          font-size: 14px;
          color: #ccc;
        }
      }

      .panel-login {
        padding: 8px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .van-icon {
          font-size: 20px;
          color: #fff;
        }
      }
    }

    .panel-actions {
      display: flex;
      justify-content: space-around;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .action-item {
        padding: 4px 6px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .van-icon {
          font-size: 18px;
          color: #fff;
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #999;

    .empty-icon {
      margin-bottom: 16px;

      .van-icon {
        font-size: 48px;
      }
    }

    .empty-text {
      font-size: 18px;
      margin-bottom: 8px;
    }

    .empty-description {
      font-size: 14px;
      line-height: 1.4;
    }
  }
}
</style>
